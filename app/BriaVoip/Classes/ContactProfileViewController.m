//
//  ContactProfileViewController.m
//  BriaVoip
//
//  Created by <PERSON> on 2015-02-18.
//  Copyright (c) 2015 CounterPath Corporation Inc. All rights reserved.
//

@import Services;

#import "ContactProfileViewController.h"
#import "ContactProfileItemLabelSelectorViewController.h"
#import "ContactProfileTonesSelectorViewController.h"
#import "SelectorViewController.h"
#import "AppController.h"
#import "ContactDetailsView.h"
#import "PresenceTitleView.h"
#import "CPActionSheet.h"
#import "NewContactProfileViewController.h"
#import "BroadcastNotifications.h"
#import "Bria-Swift.h"

#define LOG_SUBSYSTEM kLogSubApp



@interface ContactProfileViewController () <ContactListModelObserver>

@property (nonatomic, strong) PresenceTitleView* presenceTitleView;

- (void)setCustomNavigationBar;
- (void)showLabelSelectionsForIndexPath:(NSIndexPath*)indexPath;
- (void)showToneSelectionForIndexPath:(NSIndexPath*)indexPath;

@end



@implementation ContactProfileViewController

@synthesize delegate = _delegate;
@synthesize contact = _contact;
@synthesize account = _account;
@synthesize allowsEditing = _allowsEditing;
@synthesize allowsDeleting = _allowsDeleting;
@synthesize hideExtFieldsView = _hideExtFieldsView;
@synthesize hidePresence = _hidePresence;
@synthesize phoneNumbersOnly = _phoneNumbersOnly;
@synthesize presenceTitleView = _presenceTitleView;
@synthesize showHistory = _showHistory;

CallBlocking* _callBlockingService;

- (instancetype)init
{
	self = [super init];
	
	if (self)
	{
		_allowsEditing = YES;
		_allowsDeleting = YES;
		_showHistory = YES;
		self.displayButtons = YES;
		_callBlockingService = [AppController instance].callBlockingService;
	}
	
	return self;
}

- (void)viewDidLoad
{
	[super viewDidLoad];
	
	self.definesPresentationContext = NO;
	self.view.accessibilityIdentifier = @"vContact";
	
	ContactListModel *model = [AppController getContactsControl].contactsModel;
	[model addObserver:self];
	
	if([CpcServices instance].cpcContacts.contactStore && [AppFeature getBool:kFeatureHideSoftphoneNumber] == FALSE && _hideExtFieldsView == FALSE)
	{
		_impsAccount = [self _getImpsAccount];
	}
	
	[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWentInactiveNotification:) name:kAppWentInactiveNotification object:nil];
	[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(callBlockingListChangeNotification:) name:kCallBlockingListDidChange object:nil];
	[self registerForHistoryChangeEvent];
	
	self.editing = NO;
}

- (void)viewWillAppear:(BOOL)animated
{
	[super viewWillAppear:animated];
	
	[self setCustomNavigationBar];
}

- (void)dealloc
{
	[[NSNotificationCenter defaultCenter] removeObserver:self];
	[self unregisterFromHistoryChangeEvent];
	
	ContactListModel *model = [AppController getContactsControl].contactsModel;
	[model removeObserver:self];

	if (_imSelectorController)
	{
		[_imSelectorController.navigationController dismissViewControllerAnimated:NO completion:nil];
		_imSelectorController = nil;
	}
}

- (void) appWentInactiveNotification:(NSNotification*)notification
{
	if (_imSelectorController)
	{
		[_imSelectorController.navigationController dismissViewControllerAnimated:NO completion:nil];
		_imSelectorController = nil;
	}
}

- (void) callBlockingListChangeNotification:(NSNotification*)notification
{
	[self setEditing:self.editing];
}

#pragma mark - Public Methods
- (CpcAccount *)account
{
	if (_account.enabled)
		return _account;
	
	return [CpcServices instance].cpcDefaultSipAccount;
}

- (void)setAccount:(CpcAccount *)account
{
	_account = account;
	_impsAccount = account;
}

- (void)setContact:(CpcContact *)contact
{
	_contact = contact;
	
	if(self.displayButtons)
	{
		self.buttonsView.contact = contact;
	}
	
	if([self isViewLoaded])
	{
		[self setEditing:self.editing];
	}
}

- (void)setCustomView:(UIView *)customView
{
	if(customView != nil)
	{
		if (![self isViewLoaded])
			[self view]; // force to load view

		customView.frame = CGRectMake(0, 0, self.tableView.bounds.size.width, customView.bounds.size.height);

		ContactDetailsView* detailView = [[ContactDetailsView alloc] initWithFrame:CGRectMake(0, 0, self.tableView.bounds.size.width, customView.bounds.size.height + 14)];
		
		[detailView addSubview:customView];
		
		NSDictionary *views = NSDictionaryOfVariableBindings(customView);
		[detailView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"|[customView]|" options:0 metrics:nil views:views]];
		[detailView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|-7-[customView]-7-|" options:0 metrics:nil views:views]];
		
		[detailView layoutIfNeeded];

		[super setCustomView:detailView];
	}
	else
	{
		[super setCustomView:nil];
	}
}

- (void)setEditing:(BOOL)editing
{
	[super setEditing:editing];
	
	[self setPadModal:editing];
}

- (void)setShowHistory:(BOOL)showHistory
{
	if(showHistory != _showHistory)
	{
		_showHistory = showHistory;
		
		if([self isViewLoaded])
			self.editing = self.editing;
	}
}

- (BOOL)saveChanges
{
	BOOL success = [self processAndValidateData];
	if (success)
	{
		if(![self isKindOfClass:[NewContactProfileViewController class]] &&
			 [[CpcServices instance].cpcContacts getContact:_contact.identifier] == nil)
		{
			[UIAlertController showOkMessage:NSLocalizedString(@"Error", @"") message:NSLocalizedString(@"Cannot save changes, contact no longer exists", @"")];
			return NO;
		}
		
		if(self.photoChanged)
		{
			_contact.photo = self.photo;
		}

		[[CpcServices instance].cpcContacts updateContact:_contact];
		[self setCustomNavigationBar];
		
		if ([_delegate respondsToSelector:@selector(contactProfileDidSaveEditing:)])
			[_delegate contactProfileDidSaveEditing:self]; 
	}
	
	return success;
}

- (void)cancelChanges
{
	CpcContact* contact = [[CpcServices instance].cpcContacts getContact:_contact.identifier];
	self.contact = contact ? contact : _contact;
	
	if (_imSelectorController)
	{
		[_imSelectorController dismissViewControllerAnimated:YES completion:NULL];
		_imSelectorController = nil;
	}
	
	if ([_delegate respondsToSelector:@selector(contactProfileDidCancelEditing:)])
		[_delegate contactProfileDidCancelEditing:self];
}

- (void)performDefaultActionForProperty:(CpcContactProperty)property item:(GroupedItem *)item
{
	CpcContact *contact = (CpcContact*)item.referenceObject[@"contact"];
	NSInteger index = [(NSNumber*)item.referenceObject[@"index"] integerValue];
	
	id value = nil;
	id<ContactNumberDescriptor> number = nil;
	CpcAccount* account = nil;
	
	switch (property)
	{
		case ECpcContactPhoneNumberProperty:
			if (index>=0 && index<contact.phoneNumbers.count)
			{
				value = contact.phoneNumbers[index];
				number = [(CNLabeledValue<CNPhoneNumber*>*)value value].stringValue;
			}
			break;
		case ECpcContactSoftphoneProperty:
			if (index>=0 && index<contact.softphones.count)
			{
				value = contact.softphones[index];
				NSString *addr = [[(CNLabeledValue<CpcSipUri*>*)value value] aor];
				number = addr;
				account = [CpcUtilsServices getAccountByUuid:[[(CNLabeledValue<CpcSipUri*>*)value value] parameter:@"account"]];
				account = account ?: [AppPhoneUtils findBestSipCallAccountForAddress:addr];
			}
			break;
		case ECpcContactEmailProperty:
			if (index>=0 && index<contact.emails.count)
			{
				value = contact.emails[index];
				number = [(CNLabeledValue<NSString*>*)value value];
			}
			break;
		case ECpcContactURLProperty:
			if (index>=0 && index<contact.URLs.count)
			{
				value = contact.URLs[index];
				NSString* addr = [(CNLabeledValue<NSString*>*)value value];
				number = addr;
				
				NSURL *url = [NSURL URLWithString:addr];
				if([url.scheme.lowercaseString isEqualToString:@"ptt"])
				{
					number = url;
				}
			}
			break;
		case ECpcContactIMProperty:
			if (index>=0 && index<contact.IMs.count)
			{
				value = contact.IMs[index];
				number = [(CNLabeledValue<CNInstantMessageAddress*>*)value value].username;
			}
			break;
		case ECpcContactIMAddressProperty:
			value = contact.imAddress;
			number = [(CpcSipUri*)value aor];
			account = [CpcUtilsServices getAccountByUuid:[(CpcSipUri*)value parameter:@"account"]];
			break;
		case ECpcContactCommunityNumberProperty:
			if (index>=0 && index<contact.communityNumbers.numbers.count)
			{
				value = contact.communityNumbers.numbers[index];
				number = [(CpcNumber*)value number];
			}
			break;
		default:
			break;
	}
	
	LOG2(EDebug, @"property = %i, value = %@", property, value)
	
	if(value == nil)
		return;
	
	CpcContact *c = [[CpcServices instance].cpcContacts getContact:contact.identifier];

	if(c == nil)
		contact = nil; // this is a new contact
	
	if (_delegate && [_delegate respondsToSelector:@selector(contactProfileViewController:shouldPerformDefaultActionForContact:property:value:)])
	{
		if (![_delegate contactProfileViewController:self shouldPerformDefaultActionForContact:contact property:property value:value])
			return;
	}
	
	id<ContactsControl> contactsControl = [AppController getContactsControl];
	if ([contactsControl performDefaultActionForContact:contact
																							 number:number
																						 property:property
																					displayName:_contact.fullName
																							account:account ? account : self.account
																							options:0
																					 targetView:nil
																					 fromClass:[self class]])
	{
		[super performDefaultActionForProperty:property item:item];
	}
}

- (void)prepareDisplayMode
{
	[super prepareDisplayMode];
	
	if (_allowsEditing == NO)
		self.navigationItem.rightBarButtonItem = nil;
	
	// Headers
	self.photo = [_contact thumbnailOrPhoto];
	self.acronym = [_contact.acronym stringByNormalizeAcronym];
	
	NSMutableArray* items = [NSMutableArray array];
	GroupedItem *fullNameItem = [GroupedItem createWithLabel:_contact.fullName value:nil itemId:PersonProfileItemIdFullName type:EGroupedItemSingleLabel delegate:self];
	fullNameItem.accessibilityIdentifiers = @[@"lFullName"];
	[items addItem:fullNameItem];
	
	if ([_contact.displayName length])
	{
		GroupedItem *item = [GroupedItem createWithLabel:[NSString stringWithFormat:@"\"%@\"", _contact.displayName] value:nil itemId:PersonProfileItemIdDisplayName type:EGroupedItemSingleLabel delegate:self];
		item.accessibilityIdentifiers = @[@"lDisplayName"];
		[items addItem:item];
	}
	
	NSString* jobDepartment = [NSString stringWithFormat:@"%@%@%@", [_contact.job length]?_contact.job:@"", [_contact.job length] && [_contact.department length] ?  @" - ":@"", [_contact.department length]?_contact.department:@""];
	if ([jobDepartment length])
	{
		GroupedItem *item = [GroupedItem createWithLabel:jobDepartment value:nil itemId:PersonProfileItemIdJobTitle type:EGroupedItemSingleLabel delegate:self];
		item.accessibilityIdentifiers = @[@"lJobDepartment"];
		[items addItem:item];
	}
	
	if (![_contact.fullName isEqualToString:_contact.company] && [_contact.company length])
	{
		GroupedItem *item = [GroupedItem createWithLabel:_contact.company value:nil itemId:PersonProfileItemIdCompany type:EGroupedItemSingleLabel delegate:self];
		item.accessibilityIdentifiers = @[@"lCompany"];
		[items addItem:item];
	}
	
	[self.headerFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdHeaders]];
	
	// CommunityNumbers
	[items removeAllObjects];
	NSInteger i = 0;
	
	for (CpcNumber *number in _contact.communityNumbers.numbers)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"user-id", @"user match label") value:number.number itemId:PersonProfileItemIdUserId type:EGroupedItemContactTextDisplay delegate:self];
		item.accessibilityIdentifiers = @[@"lCommunity", @"tCommunity"];
		
		item.referenceObject = @{@"contact": _contact, @"index":@(i)};
		[items addItem:item];
		
		++i;
	}

	[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdUserIds]];
	
	// Softphones
	[items removeAllObjects];
	
	i = 0;

	for (CNLabeledValue<CpcSipUri*> *number in _contact.softphones)
	{
		if (_phoneNumbersOnly)
		{
			// check if user portion look like a phone number
			CpcUri *uri = [CpcUri uriWithNumber:number.value.user];
			if(![uri isKindOfClass:[CpcTelUri class]])
				continue;
		}
		
		// Don't display item for anonymous phone number
		if ([CpcUtilsAddresses isAddressAnonymous:[number.value aor]])
		{
			continue;
		}
		
		NSString *aor = [number.value aor];
		
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"softphone", @"sip-uri label") value:aor itemId:PersonProfileItemIdSoftphone type:EGroupedItemContactTextDisplayWithThirdLabel delegate:self];
		item.accessibilityIdentifiers = @[@"lSoftphone", @"tSoftphone", @"lSoftphoneAccount"];
		
		item.referenceObject = @{@"contact": _contact, @"index":@(i), @"identifier":number.identifier};

		if(NO == self.hideSipRecordsAccounts)
		{
			CpcAccount* assignedAccount = [CpcUtilsServices getAccountByUuid:[number.value parameter:@"account"]];
			NSString* accountName = assignedAccount.accountName.length ? assignedAccount.accountName : NSLocalizedString(@"Empty", @"Empty account");
			
			item.descriptionText = assignedAccount ? [NSString stringWithFormat:NSLocalizedString(@"Account: %@", @""), accountName] : nil;
		}
		
		if ([_contact.communityNumbers hasNumber:aor ignoreDomain:NO])
		{
			item.accessoryView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_contacts_user_match"]];
		}

		[items addItem:item];
		
		++i;
	}
	
	[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdSoftphones]];
	
	// IMPS
	[items removeAllObjects];
	
	CpcServices* services = [CpcServices instance];
	
	if([CpcFeature getBool:kFeatureIm] && _hidePresence == FALSE && services.cpcOptions.messagesIM && [AppPhoneUtils getEnabledSipPresenceAccountsCount] > 0 && ([_contact.softphones count] > 0 || [_contact.communityNumbers.numbers count] > 0) && !_phoneNumbersOnly && _contact.imAddress != nil)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"IM URI", @"") value:[_contact.imAddress aor] itemId:PersonProfileItemIdIMPS type:EGroupedItemContactTextDisplayWithThirdLabel delegate:self];
		item.accessibilityIdentifiers = @[@"lIM", @"lIMAddress"];
		
		item.referenceObject = @{@"contact": _contact};
		if(NO == self.hideSipRecordsAccounts)
		{
			CpcAccount* assignedAccount = [CpcUtilsServices getAccountByUuid:[_contact.imAddress parameter:@"account"]];
			NSString* accountName = assignedAccount.accountName.length ? assignedAccount.accountName : NSLocalizedString(@"Empty", @"Empty account");
			item.descriptionText = assignedAccount ? [NSString stringWithFormat:NSLocalizedString(@"Account: %@", @""), accountName] : nil;
		}
		[items addItem:item];

		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdImPresence]];
	}
	
	// Telephones
	[items removeAllObjects];
	for (NSInteger i = 0; i < [_contact.phoneNumbers count]; i++)
	{
		GroupedItem* item = [GroupedItem createWithLabel:_contact.phoneNumbers[i].label
																							 value:_contact.phoneNumbers[i].value.stringValue
																							itemId:PersonProfileItemIdPhone
																								type:EGroupedItemContactTextDisplay
																						delegate:self];
		item.accessibilityIdentifiers = @[@"lPhone", @"tPhone"];
		item.referenceObject = @{@"contact": _contact, @"index":@(i), @"identifier":_contact.phoneNumbers[i].identifier};
		
#if 0
		NSString *number = contact.phoneNumbers[i].value.stringValue;
		if ([contact.communityNumbers hasNumber:number ignoreDomain:YES])
		{
			item.accessoryView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"icon_contacts_user_match"]];
		}
#endif
		
		[items addItem:item];
	}
	[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdPhones]];
	
	// Emails
	if(!_phoneNumbersOnly)
	{
		[items removeAllObjects];
		for (NSInteger i = 0; i < [_contact.emails count]; i++)
		{
			GroupedItem* item = [GroupedItem createWithLabel:_contact.emails[i].label
																								 value:_contact.emails[i].value
																								itemId:PersonProfileItemIdEmail
																									type:EGroupedItemContactTextDisplay
																							delegate:self];
			item.accessibilityIdentifiers = @[@"lEmail", @"tEmail"];
			item.referenceObject = @{@"contact": _contact, @"index":@(i), @"identifier":_contact.emails[i].identifier};
			[items addItem:item];
		}
		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdEmails]];
		
		// Tones
//		[items removeAllObjects];
//		if ([_contact.ringtone length])
//		{
//			[items addItem:[GroupedItem createWithLabel:NSLocalizedString(@"Ringtone", @"")
//																						value:_contact.ringtone
//																					 itemId:PersonProfileItemIdRingtone
//																						 type:EGroupedItemContactTextDisplay
//																				 delegate:self]];
//		}
//		if ([_contact.texttone length])
//		{
//			[items addItem:[GroupedItem createWithLabel:NSLocalizedString(@"Texttone", @"")
//																						value:_contact.texttone
//																					 itemId:PersonProfileItemIdTexttone
//																						 type:EGroupedItemContactTextDisplay
//																				 delegate:self]];
//		}
//		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdTones]];
//		
		// URLs
		[items removeAllObjects];
		for (NSInteger i = 0; i < [_contact.URLs count]; i++)
		{
			GroupedItem* item = [GroupedItem createWithLabel:_contact.URLs[i].label
																								 value:_contact.URLs[i].value
																								itemId:PersonProfileItemIdURL
																									type:EGroupedItemContactTextDisplay
																							delegate:self];
			item.accessibilityIdentifiers = @[@"lURL", @"tURL"];
			item.referenceObject = @{@"contact": _contact, @"index":@(i), @"identifier":_contact.URLs[i].identifier};
			[items addItem:item];
		}
		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdURLs]];
		
#if 0 // IMs: disabling for now. no openURL scheme to initiate action
		[items removeAllObjects];
		for (CpcContact* contact in _linkedContacts)
		{
			for (NSInteger i = 0; i < [contact.IMs count]; i++)
			{
				NSDictionary* im = [contact.IMs valueAtIndex:i];
				GroupedItem* item = [GroupedItem createWithLabel:im[(NSString*)kABPersonInstantMessageServiceKey]
																									 value:im[(NSString*)kABPersonInstantMessageUsernameKey]
																									itemId:PersonProfileItemIdIM
																										type:EGroupedItemContactTextDisplay
																								delegate:self];
				item.referenceObject = @{@"contact": contact, @"index":@(i), @"identifier":_contact.IMs[i].identifier};
				[items addItem:item];
			}
		}
		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdIMs]];
#endif
		
		[items removeAllObjects];
		
		if([AppFeature getBool:kFeatureDisableCallBlocking] == FALSE &&
			 [AppController instance].appSettings.enableCallBlock &&
			 [AppController instance].appSettings.showCallBlock &&
			 [AppController instance].appSettings.supportsCallBlockUserEntries)
		{
			CpcUri *uri = [_callBlockingService makeCpcBlockNumberFrom:[self phoneNumberToBlock]];
			
			if(uri != nil && [_callBlockingService isBlockable:uri])
			{
				GroupedItem* item = nil;
				if([_callBlockingService isAddedToBlockList:uri] == false)
				{
					item = [GroupedItem createWithLabel:nil value:[NSString stringWithFormat:NSLocalizedString(@"Block %@", @"button title"), uri.user] itemId:PersonProfileItemIdBlockCallerButton type:EGroupedItemButton delegate:self];
					item.readonly = [_callBlockingService availableBlockSlots] < 1;
					item.accessibilityIdentifiers = @[@"bBlockNumber"];
				}
				else if([_callBlockingService hasUserEntry:uri])
				{
					item = [GroupedItem createWithLabel:nil value:[NSString stringWithFormat:NSLocalizedString(@"Unblock %@", @"button title"), uri.user] itemId:PersonProfileItemIdBlockCallerButton type:EGroupedItemButton delegate:self];
					item.accessibilityIdentifiers = @[@"bUnblockNumber"];
				}
				
				if(item != nil)
				{
					item.referenceObject = uri;
					[items addItem:item];
					
					[self.footerFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdActions]];
				}
			}
		}
	}
	
	if(self.displayButtons)
	{
		self.buttonsView.contact = self.contact;
	}
	
	if(!_phoneNumbersOnly && _showHistory && self.contact != nil)
	{
		[self updateCallLogEntries];
	}
	else
	{
		self.callLogEntries = nil;
	}
	
	if(self.callLogEntries != nil)
	{
		// create section with fake GroupedItem, self.callLogEntries is used as a source instead
		[self.footerFields addSection:[GroupedSection create:nil items:@[[GroupedItem create]] sectionId:PersonProfileSectionIdHistory]];
	}
}

- (void)prepareEditMode
{
	[super prepareEditMode];
	
	if (_contact == nil) {
		LOG(EError, @"Contact is nil, cannot prepare edit mode.");
		return;
	}
	
	// Headers
	if(NO == self.photoChanged)
	{
		self.photo = [_contact thumbnailOrPhoto];
	}
	
	self.acronym = [_contact.acronym stringByNormalizeAcronym];
	
	NSMutableArray* items = [NSMutableArray array];
	
	if (_contact.prefixName)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"Prefix", @"buddy profile name label") value:_contact.prefixName itemId:PersonProfileItemIdPrefixName type:EGroupedItemInputTextNoLabel delegate:self];
		item.accessibilityIdentifiers = @[@"tPrefix"];
		[items addItem:item];
	}
	
	GroupedItem *itemFirst = [GroupedItem createWithLabel:NSLocalizedString(@"First", @"buddy profile name place holder") value:_contact.firstName itemId:PersonProfileItemIdFirstName type:EGroupedItemInputTextNoLabel delegate:self];
	itemFirst.accessibilityIdentifiers = @[@"tFirst"];
	[items addItem:itemFirst];
	
	if (_contact.middleName)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"Middle", @"buddy profile name label") value:_contact.middleName itemId:PersonProfileItemIdMiddleName type:EGroupedItemInputTextNoLabel delegate:self];
		item.accessibilityIdentifiers = @[@"tMiddle"];
		[items addItem:item];
	}
	
	GroupedItem *itemLast = [GroupedItem createWithLabel:NSLocalizedString(@"Last", @"buddy profile name place holder") value:_contact.lastName itemId:PersonProfileItemIdLastName type:EGroupedItemInputTextNoLabel delegate:self];
	itemLast.accessibilityIdentifiers = @[@"tLast"];
	[items addItem:itemLast];
	
	if (_contact.suffixName)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"Suffix", @"buddy profile name label") value:_contact.suffixName itemId:PersonProfileItemIdSuffixName type:EGroupedItemInputTextNoLabel delegate:self];
		item.accessibilityIdentifiers = @[@"tSuffix"];
		[items addItem:item];
	}
	
	if (_contact.displayName)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"Nickname", @"buddy profile name label") value:_contact.displayName itemId:PersonProfileItemIdDisplayName type:EGroupedItemInputTextNoLabel delegate:self];
		item.accessibilityIdentifiers = @[@"tNickname"];
		[items addItem:item];
	}
	
	if (_contact.job)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"Job Title", @"buddy profile name label") value:_contact.job itemId:PersonProfileItemIdJobTitle type:EGroupedItemInputTextNoLabel delegate:self];
		item.accessibilityIdentifiers = @[@"tJob"];
		[items addItem:item];
	}
	
	if (_contact.department)
	{
		GroupedItem *item = [GroupedItem createWithLabel:NSLocalizedString(@"Department", @"buddy profile name label") value:_contact.department itemId:PersonProfileItemIdDepartment type:EGroupedItemInputTextNoLabel delegate:self];
		item.accessibilityIdentifiers = @[@"tDepartment"];
		[items addItem:item];
	}
	
	GroupedItem *companyItem = [GroupedItem createWithLabel:NSLocalizedString(@"Company", @"buddy profile name label") value:_contact.company itemId:PersonProfileItemIdCompany type:EGroupedItemInputTextNoLabel delegate:self];
	companyItem.accessibilityIdentifiers = @[@"tCompany"];
	[items addItem:companyItem];
	
	[self.headerFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdHeaders]];
	
	for(GroupedItem* item in items)
	{
		[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
	}
	
	// Bodies
	
	// CommunityNumbers
	[items removeAllObjects];
	NSInteger i = 0;
	
	for (CpcNumber *number in _contact.communityNumbers.numbers)
	{
		Elem* elem = [Elem create];
		elem.readonly = YES;
		GroupedItem *item = [GroupedItem create:NSLocalizedString(@"user-id", @"user match label") value:number.number itemId:PersonProfileItemIdUserId type:EGroupedItemContactInputText elem:elem delegate:self];
		item.accessibilityIdentifiers = @[@"lCommunity", @"tCommunity"];
		
		item.referenceObject = @{@"contact": _contact, @"index":@(i)};
		[items addItem:item];
		[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
		
		++i;
	}

	[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdUserIds]];
	
	// Softphones
	[items removeAllObjects];

	i = 0;

	for (CNLabeledValue<CpcSipUri*> *number in _contact.softphones)
	{
		if (_phoneNumbersOnly)
		{
			// check if user portion look like a phone number
			CpcUri *uri = [CpcUri uriWithNumber:number.value.user];
			if(![uri isKindOfClass:[CpcTelUri class]])
				continue;
		}
		
		NSString *value = [number.value parameter:@"newContact"] != nil ? @"": number.value.aor;
		
		GroupedItem *item = [GroupedItem create:NSLocalizedString(@"softphone", @"sip-uri label") value:value itemId:PersonProfileItemIdSoftphone type:EGroupedItemContactInputText elem:nil delegate:self];
		item.accessibilityIdentifiers = @[@"lSoftphone", @"tSoftphone"];

		item.referenceObject = @{@"contact": _contact, @"index":@(i)};
		[items addItem:item];
		[self setTextFieldItem:item isEmpty:([value length] == 0)];

		++i;
	}
	
	GroupedItem *addSoftphone = [GroupedItem createWithLabel:NSLocalizedString(@"add softphone", @"contact view label") value:nil itemId:PersonProfileItemIdAddItemAction type:EGroupedItemContactEntry delegate:self];
	addSoftphone.accessibilityIdentifiers = @[@"lAddSoftphone"];
	[items addItem:addSoftphone];
	
	[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdSoftphones]];
	
	// IMPS
	[items removeAllObjects];
	
	CpcServices* services = [CpcServices instance];
	
	if([CpcFeature getBool:kFeatureIm] && _hidePresence == FALSE && services.cpcOptions.messagesIM && [AppPhoneUtils getEnabledSipPresenceAccountsCount] > 0 && !_phoneNumbersOnly)
	{
		GroupedItem *item = [GroupedItem create:NSLocalizedString(@"IM URI", @"") value:[_contact.imAddress aor] itemId:PersonProfileItemIdIMPS type:EGroupedItemContactEntry elem:nil delegate:self];
		item.accessibilityIdentifiers = @[@"lIM", @"lIMAddress"];
		
		item.referenceObject = @{@"contact": _contact};
		[items addItem:item];

		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdImPresence]];
	}
	
	// Telephones
	[items removeAllObjects];
	for (NSInteger i = 0; i < [_contact.phoneNumbers count]; i++)
	{
		GroupedItem* item = [GroupedItem createWithLabel:_contact.phoneNumbers[i].label
																							 value:_contact.phoneNumbers[i].value.stringValue
																							itemId:PersonProfileItemIdPhone
																								type:EGroupedItemContactInputText
																						delegate:self];
		item.accessibilityIdentifiers = @[@"lPhone", @"tPhone"];
		
		item.referenceObject = @{@"contact": _contact, @"index":@(i)};
		[items addItem:item];
		[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
	}
	
	GroupedItem *addPhone = [GroupedItem createWithLabel:NSLocalizedString(@"add phone", @"contact view label") value:nil itemId:PersonProfileItemIdAddItemAction type:EGroupedItemContactEntry delegate:self];
	addPhone.accessibilityIdentifiers = @[@"lAddPhone"];
	[items addItem:addPhone];
	
	[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdPhones]];
	
	if(!_phoneNumbersOnly)
	{
		// Emails
		[items removeAllObjects];
		
		for (NSInteger i = 0; i < [_contact.emails count]; i++)
		{
			GroupedItem* item = [GroupedItem createWithLabel:_contact.emails[i].label
																								 value:_contact.emails[i].value
																								itemId:PersonProfileItemIdEmail
																									type:EGroupedItemContactInputText
																							delegate:self];
			item.accessibilityIdentifiers = @[@"lEmail", @"tEmail"];
			item.referenceObject = @{@"contact": _contact, @"index":@(i)};
			[items addItem:item];
			[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
		}
		GroupedItem *addEmail = [GroupedItem createWithLabel:NSLocalizedString(@"add email", @"contact view label") value:nil itemId:PersonProfileItemIdAddItemAction type:EGroupedItemContactEntry delegate:self];
		addEmail.accessibilityIdentifiers = @[@"lAddEmail"];
		[items addItem:addEmail];
		
		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdEmails]];
		
		// Tones
//		[items removeAllObjects];
//		if ([_contact.ringtone length])
//		{
//			GroupedItem* item = [GroupedItem createWithLabel:NSLocalizedString(@"Ringtone", @"")
//																								 value:_contact.ringtone
//																								itemId:PersonProfileItemIdRingtone
//																									type:EGroupedItemContactEntry
//																							delegate:self];
//			[items addItem:item];
//			[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
//		}
//		if ([_contact.texttone length])
//		{
//			GroupedItem* item = [GroupedItem createWithLabel:NSLocalizedString(@"Texttone", @"")
//																								 value:_contact.texttone
//																								itemId:PersonProfileItemIdTexttone
//																									type:EGroupedItemContactEntry
//																							delegate:self];
//			[items addItem:item];
//			[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
//		}
//		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdTones]];
		
		// URLs
		[items removeAllObjects];
		for (NSInteger i = 0; i < [_contact.URLs count]; i++)
		{
			GroupedItem* item = [GroupedItem createWithLabel:_contact.URLs[i].label
																								 value:_contact.URLs[i].value
																								itemId:PersonProfileItemIdURL
																									type:EGroupedItemContactInputText
																							delegate:self];
			item.accessibilityIdentifiers = @[@"lURL", @"tURL"];
			item.referenceObject = @{@"contact": _contact, @"index":@(i)};
			[items addItem:item];
			[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
		}
		
		GroupedItem *addUrl = [GroupedItem createWithLabel:NSLocalizedString(@"add URL", @"contact view label") value:nil itemId:PersonProfileItemIdAddItemAction type:EGroupedItemContactEntry delegate:self];
		addUrl.accessibilityIdentifiers = @[@"lAddURL"];
		[items addItem:addUrl];
		
		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdURLs]];
		
#if 0 // IMs: disabling for now. no generic openURL scheme to initiate action
		[items removeAllObjects];
		for (CpcContact* contact in linkedContacts)
		{
			for (NSInteger i = 0; i < [contact.IMs count]; i++)
			{
				NSDictionary* im = [contact.IMs valueAtIndex:i];
				GroupedItem* item = [GroupedItem createWithLabel:im[(NSString*)kABPersonInstantMessageServiceKey]
																									 value:im[(NSString*)kABPersonInstantMessageUsernameKey]
																									itemId:PersonProfileItemIdIM
																										type:EGroupedItemContactInputText
																								delegate:self];
				item.referenceObject = @{@"contact": contact, @"index":@(i)};
				[items addItem:item];
				[self setTextFieldItem:item isEmpty:([item.value length] == 0)];
			}
		}
		[items addItem:[GroupedItem createWithLabel:NSLocalizedString(@"add IM", @"contact view label") value:nil itemId:PersonProfileItemIdAddItemAction type:EGroupedItemContactEntry delegate:self]];
		[self.bodyFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdIMs]];
#endif
	}
	
	if (self.allowsDeleting)
	{
		// Delete Button
		[items removeAllObjects];
		GroupedItem *item = [GroupedItem createWithLabel:nil value:NSLocalizedString(@"Delete Contact", @"") itemId:PersonProfileItemIdDeletePersonAction type:EGroupedItemButton delegate:self];
		item.accessibilityIdentifiers = @[@"bDeleteContact"];
		[items addItem:item];
		[self.footerFields addSection:[GroupedSection create:nil items:items sectionId:PersonProfileSectionIdActions]];
	}
	
	self.callLogEntries = nil;
}

- (BOOL) processAndValidateData
{
	// softphones
	NSMutableArray<CNLabeledValue<CpcSipUri*>*> *softphones = [[NSMutableArray alloc] init];
	NSArray<CNLabeledValue<CpcSipUri*>*> *currentSoftphones = _contact.softphones;
	
	for (CNLabeledValue<CpcSipUri*> *softphone in currentSoftphones)
	{
		if([softphone.value parameter:@"newContact"] != nil)
			continue;
		
		if (softphone.value.host.length == 0 && softphone.value.user.length == 0)
			continue;
		
		if (softphone.value.host.length == 0 || softphone.value.user.length == 0)
		{
			[UIAlertController showOkMessage:NSLocalizedString(@"Saving failed", @"contact saving") message:[NSString stringWithFormat:NSLocalizedString(@"Softphone '%@' does not have domain. Append domain and save again.", @""), [softphone.value aor]]];
			return NO;
		}
		
		[softphones addObject:softphone];
	}
	
	if(currentSoftphones.count != softphones.count)
		_contact.softphones = softphones;

	// phone numbers
	NSMutableArray<CNLabeledValue<CNPhoneNumber*>*> *phoneNumbers = [[NSMutableArray alloc] init];

	for (CNLabeledValue<CNPhoneNumber*> *phoneNumber in _contact.phoneNumbers)
	{
		if(phoneNumber.value.stringValue.length != 0)
			[phoneNumbers addObject:phoneNumber];
	}
	
	if(_contact.phoneNumbers.count != phoneNumbers.count)
		_contact.phoneNumbers = phoneNumbers;

	// emails
	NSMutableArray<CNLabeledValue<NSString*>*> *emails = [[NSMutableArray alloc] init];
	
	for (CNLabeledValue<NSString*> *email in _contact.emails)
	{
		if(email.value.length != 0)
			[emails addObject:email];
	}
	
	if(_contact.emails.count != emails.count)
		_contact.emails = emails;

	// URLs
	NSMutableArray<CNLabeledValue<NSString*>*> *urls = [[NSMutableArray alloc] init];
	
	for (CNLabeledValue<NSString*> *url in _contact.URLs)
	{
		if(url.value.length != 0)
			[urls addObject:url];
	}
	
	if(_contact.URLs.count != urls.count)
		_contact.URLs = urls;
	

	// IMs
	NSMutableArray<CNLabeledValue<CNInstantMessageAddress*>*> *ims = [[NSMutableArray alloc] init];
	
	for (CNLabeledValue<CNInstantMessageAddress*> *im in _contact.IMs)
	{
		if(im.value.username.length != 0)
			[ims addObject:im];
	}
	
	if(_contact.IMs.count != ims.count)
		_contact.IMs = ims;
	
	return YES;
}

- (NSString*)phoneNumberToBlock
{
	NSString* number = nil;
	
	if(self.highlightedIdentifier != nil)
	{
		for(CNLabeledValue<CpcSipUri*>* softphone in _contact.softphones)
		{
			if([softphone.identifier isEqualToString:self.highlightedIdentifier])
			{
				if (_phoneNumbersOnly)
				{
					CpcUri *uri = [CpcUri uriWithNumber:softphone.value.user];
					if([uri isKindOfClass:[CpcTelUri class]])
					{
						number = softphone.value.user;
					}
				}
				else
				{
					number = [softphone.value aor];
				}
				break;
			}
		}
		
		if(number == nil)
		{
			for(CNLabeledValue<CNPhoneNumber*>* phoneNumber in _contact.phoneNumbers)
			{
				if([phoneNumber.identifier isEqualToString:self.highlightedIdentifier])
				{
					number = phoneNumber.value.stringValue;
					break;
				}
			}
		}
	}
	
	return number;
}

#pragma mark - Private Methods
- (void) setCustomNavigationBar
{
		self.title = [[_contact fullName] length] ? [_contact fullName] : NSLocalizedString(@"Info", "");
	
    CpcBuddy* buddy = [[CpcServices instance].cpcBuddies getBuddyByContactId:_contact.identifier];
    if (buddy == nil || buddy.presenceInfo.presenceStatus == EPresenceStatusNone)
    {
        _presenceTitleView = nil;
    }
    else
    {
        CGRect viewRect = CGRectMake(0, 0, CGRectInset(self.navigationController.navigationBar.frame, 40, 0).size.width, 40);
        _presenceTitleView = [[PresenceTitleView alloc] initWithFrame:viewRect];
        _presenceTitleView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
        [_presenceTitleView setBuddy:buddy];
    }

    self.navigationItem.titleView = _presenceTitleView;
}

- (void)showLabelSelectionsForIndexPath:(NSIndexPath*)indexPath
{
    GroupedSection* section = self.bodyFields[indexPath.section];
    GroupedItem* item = section.items[indexPath.row];
    ContactProfileItemLabelSelectorViewController* selectorViewController = [[ContactProfileItemLabelSelectorViewController alloc] initWithItem:item sourceAccessibilityIdentifier:self.view.accessibilityIdentifier];
    __weak ContactProfileItemLabelSelectorViewController* weakRef = selectorViewController;
    selectorViewController.completionHandler = ^(ContactProfileItemLabelSelectorViewControllerResult result) {
        [self.view findAndResignFirstResponder];
        [self dismissViewController:weakRef];
        if (result == ContactProfileTonesSelectorViewControllerResultDone)
        {
            if ([weakRef.selectedLabel length] && ![weakRef.selectedLabel isEqualToString:item.label])
            {
                item.label = weakRef.selectedLabel;
                [item.delegate itemChanged:item];
                [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
            }
        }
    };
    
    UITableViewCell* cell = [self.tableView cellForRowAtIndexPath:indexPath];
    CGRect frame = [self.tableView convertRect:cell.textLabel.frame fromView:cell];
    [self presentViewController:[[UINavigationController alloc] initWithRootViewController:selectorViewController] fromRect:frame];
}

- (void)showToneSelectionForIndexPath:(NSIndexPath*)indexPath
{
    GroupedSection* section = self.bodyFields[indexPath.section];
    GroupedItem* item = section.items[indexPath.row];
    ContactProfileTonesSelectorViewController* selectorViewController = [[ContactProfileTonesSelectorViewController alloc] initWithItem:item];
    __weak ContactProfileTonesSelectorViewController* weakRef = selectorViewController;
    selectorViewController.completionHandler = ^(ContactProfileTonesSelectorViewControllerResult result) {
        [self.view findAndResignFirstResponder];
        [self dismissViewController:weakRef];
        if (result == ContactProfileTonesSelectorViewControllerResultDone)
        {
            if ([weakRef.selectedTone length] && ![weakRef.selectedTone isEqualToString:item.value])
            {
                item.value = weakRef.selectedTone;
                [item.delegate itemChanged:item];
                [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
                
                if (!self.tableView.editing && [self saveChanges])
                {
                    LOG(EDebug, @"Saving tone selection")
                }
            }
        }
    };
    
    UITableViewCell* cell = [self.tableView cellForRowAtIndexPath:indexPath];
    [self presentViewController:[[UINavigationController alloc] initWithRootViewController:selectorViewController] fromRect:cell.frame];
}

- (CpcAccount*) _getImpsAccount
{
	if(_account != nil && _account.enabled)
		return _account;
	
	for(CpcAccount *account in [CpcServices instance].cpcAccounts)
	{
		if(!account.enabled)
			continue;
		
		if(account.protocol != ESignalingProtocolSip)
			continue;
		
		if(!account.presence)
			continue;
		
		return account;
	}
	
	return self.account;
}

// checks if softphone number exists for presence number

- (void) updatePresenceNumberWithNumber:(NSString*)newPresenseAddress
{
	NSString *currentPres = self.contact.imAddress.aor;
	
	if([currentPres length] > 0)
	{
		BOOL presFound = NO;
		
		GroupedSection *softphoneSection = [GroupedSection getGroupedSectionById:PersonProfileSectionIdSoftphones sections:self.bodyFields];
		
		for(GroupedItem *softphoneItem in softphoneSection.items)
		{
			NSString *softphone = softphoneItem.value;
			
			if ([currentPres isEqualToString:softphone])
			{
				presFound = YES;
				break;
			}
		}
		
		if(!presFound)
		{
			GroupedSection *presSection = [GroupedSection getGroupedSectionById:PersonProfileSectionIdImPresence sections:self.bodyFields];
			
			if(presSection != nil)
			{
				GroupedItem *presenceItem = presSection.items[0];
				presenceItem.value = newPresenseAddress;
			}
			else
			{
				@try
				{
					self.contact.imAddress = [[CpcSipUri alloc] initWithString:newPresenseAddress];
				}
				@catch (NSException *exception)
				{
					self.contact.imAddress = nil;
				}
			}
		}
	}
}

- (void) showPresenceView:(BOOL)animated
{
	[self.tableView findAndResignFirstResponder];

	_imSelectorController = [[SelectorViewController alloc] init];
	_imSelectorController.navigationItem.title = NSLocalizedString(@"Presence & IM Address", @"");
	_imSelectorController.alignment = NSTextAlignmentLeft;
	_imSelectorController.section = [self preparePresenceSection];
	_imSelectorController.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemDone target:self action:@selector(presenceDone:)];
	_imSelectorController.preferredContentSize = CGSizeMake(320, 460);
	
	_imSelectorController.view.accessibilityIdentifier = [self.view.accessibilityIdentifier stringByAppendingString:@"IMUri"] ;
	
	UINavigationController* navController = nil;
	navController = [[UINavigationController alloc] initWithRootViewController:_imSelectorController];
	if([AppController isPadStyleGUI])
	{
		navController.modalPresentationStyle = UIModalPresentationCurrentContext;
	}
	else
	{
		navController.modalPresentationStyle = UIModalPresentationFullScreen;
	}
	
	//fix for ios bug - navigation bar stays partially visible in popover, if it was hidden once before (by search display controller)
	if([AppController isPadStyleGUI] && [AppGuiUtils isInPopover:self] && [AppController getDetailNavigationController].activeHomeView == EHomeViewFavorites)
	{
		[self.navigationController setNavigationBarHidden:YES animated:YES];
	}
	
	NSString* footnoteText = NSLocalizedString(@"Selected softphone number (SIP URI) is used for the IM address and presence subscription. \nYou can modify the softphone addresses list by editing the contact properties.", @"");
	_imSelectorController.tableFooterText = footnoteText;
	
	navController.modalPresentationStyle = UIModalPresentationCurrentContext;
	[self.navigationController presentViewController:navController animated:animated completion:NULL];
}

- (GroupedSection*) preparePresenceSection
{
	NSMutableArray *items = [NSMutableArray array];
	
	GroupedItem* itemNoPresence = [GroupedItem createWithLabel:NSLocalizedString(@"None", @"") value:nil itemId:PersonProfileItemIdIMPS type:EGroupedItemSingleLabel delegate:self];
	
	itemNoPresence.selected = YES;
	[items addObject:itemNoPresence];
	
	BOOL selected = NO;
	
	NSInteger i = 0;
	
	NSString *currentIm = [self.contact.imAddress aor];

	for (CpcNumber *number in self.contact.communityNumbers.numbers)
	{
		if(number.number.length == 0)
			continue;
		
		BOOL featureHideSoftphoneDomain = [AppFeature getBool:kFeatureHideSoftphoneDomain];
		NSString *displayUri = (featureHideSoftphoneDomain && ![AppController instance].appSettings.showUriDomain) ? [AppPhoneUtils numberFromUri:number.number]: number.number;
		
		GroupedItem* item = [GroupedItem createWithLabel:displayUri value:number.number itemId:PersonProfileItemIdIMPS type:EGroupedItemSingleLabel delegate:self];
		
		if(!selected && currentIm && [number.number compare:currentIm] == NSOrderedSame)
		{
			item.selected = YES;
			itemNoPresence.selected = NO;
			selected = YES;
		}
		
		[items addObject:item];
		i++;
	}

	for (CNLabeledValue<CpcSipUri*> *number in self.contact.softphones)
	{
		NSString *aor = [number.value aor];
		
		if([number.value aor].length == 0 || [number.value parameter:@"newContact"] != nil)
			continue;
		
		BOOL featureHideSoftphoneDomain = [AppFeature getBool:kFeatureHideSoftphoneDomain];
		NSString *displayUri = (featureHideSoftphoneDomain && ![AppController instance].appSettings.showUriDomain) ? number.value.user: [number.value aor];
		
		GroupedItem* item = [GroupedItem createWithLabel:displayUri value:aor itemId:PersonProfileItemIdIMPS type:EGroupedItemSingleLabel delegate:self];
		
		if(!selected && currentIm && [aor compare:currentIm] == NSOrderedSame)
		{
			item.selected = YES;
			itemNoPresence.selected = NO;
			selected = YES;
		}
		
		[items addObject:item];
		i++;
	}
	
	return [GroupedSection createWithTitle:NSLocalizedString(@"Select Softphone Address", @"") items:items sectionId:PersonProfileSectionIdImPresence];
}

- (void) presenceDone:(id)sender
{
	if(_imSelectorController)
	{
		//fix for ios bug - navigation bar stays partially visible in popover, if it was hidden once before (by search display controller)
		if([AppController isPadStyleGUI] && self.navigationController.navigationBarHidden)
		{
			[self.navigationController setNavigationBarHidden:NO animated:YES];
		}
		
		[self.navigationController dismissViewControllerAnimated:YES completion:nil];
		
		_imSelectorController = nil;
	}
}



#pragma mark - ContactListModelObserver

- (void)contactListUpdated
{
	[self setEditing:self.editing];
}

- (void)contactPreviewUpdated:(CpcContactPreview*)contactPreview
{
	if ([_contact.identifier isEqualToString:contactPreview.identifier])
	{
		CpcContact *contact = [[CpcServices instance].cpcContacts getContact:contactPreview.identifier];
		self.contact = contact;
	}
}

- (void)presenceInfoChanged:(CpcPresenceInfo*)presenceInfo forBuddy:(CpcBuddy *)buddy
{
	if (buddy != nil && buddy.descriptor.addressBookId == kCpcBuddyNativeAddressBookId)
	{
		NSString* contactId = (NSString*)buddy.descriptor.recordId;
		if ([_contact.identifier isEqualToString:contactId])
		{
			[self setCustomNavigationBar];
		}
	}
}

#pragma mark - GroupSectionDelegate
- (void) itemChanged:(GroupedItem *)item
{
	LOG3(EDebug, @"sectionId = %i, itemId = %i, value = %@", item.sectionId, item.itemId, item.value)
	
	BOOL isChanged = YES;
	switch (item.itemId)
	{
		case PersonProfileItemIdPrefixName:
		{
			if([_contact.prefixName isEqualToString:item.value] ||
				 (_contact.prefixName == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.prefixName = item.value;
		}
			break;
		case PersonProfileItemIdFirstName:
		{
			if([_contact.firstName isEqualToString:item.value] ||
				 (_contact.firstName == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.firstName = item.value;
		}
			break;
		case PersonProfileItemIdMiddleName:
		{
			if([_contact.middleName isEqualToString:item.value] ||
				 (_contact.middleName == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.middleName = item.value;
		}
			break;
		case PersonProfileItemIdLastName:
		{
			if([_contact.lastName isEqualToString:item.value] ||
				 (_contact.lastName == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.lastName = item.value;
		}
			break;
		case PersonProfileItemIdSuffixName:
		{
			if([_contact.suffixName isEqualToString:item.value] ||
				 (_contact.suffixName == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.suffixName = item.value;
		}
			break;
		case PersonProfileItemIdDisplayName:
		{
			if([_contact.displayName isEqualToString:item.value] ||
				 (_contact.displayName == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.displayName = item.value;
		}
			break;
		case PersonProfileItemIdJobTitle:
		{
			if([_contact.job isEqualToString:item.value] ||
				 (_contact.job == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.job = item.value;
		}
			break;
		case PersonProfileItemIdDepartment:
		{
			if([_contact.department isEqualToString:item.value] ||
				 (_contact.department == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.department = item.value;
		}
			break;
		case PersonProfileItemIdCompany:
		{
			if([_contact.company isEqualToString:item.value] ||
				 (_contact.company == nil && [item.value length] == 0) ||
				 ([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)))
			{
				isChanged = NO;
			}
			_contact.company = item.value;
		}
			break;
		case PersonProfileItemIdPhone:
		{
			CpcContact* contact = item.referenceObject[@"contact"];
			NSNumber* index = item.referenceObject[@"index"];
			
			BOOL valueChanged = !([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil));
			BOOL labelChanged = (!([item.label isEqualToString:item.initialLabel] || ([item.label length] == 0 && item.initialLabel == nil))
													 || !([item.label isEqualToString:item.previousLabel] || ([item.label length] == 0 && item.previousLabel == nil)));
			
			if(!valueChanged && !labelChanged)
			{
				isChanged = NO;
			}
			else
			{
				if(labelChanged && !valueChanged && [item.value isEqualToString:@""])
				{
					isChanged = NO;
				}
			}
			
			if(valueChanged || labelChanged)
			{
				NSMutableArray<CNLabeledValue<CNPhoneNumber*> *> *phoneNumbers = [contact.phoneNumbers mutableCopy];
				CNLabeledValue<CNPhoneNumber*> *oldValue = phoneNumbers[[index unsignedIntegerValue]];
				CNLabeledValue<CNPhoneNumber*> *value = [oldValue labeledValueBySettingLabel:item.label value:[[CNPhoneNumber alloc] initWithStringValue:item.value != nil ? item.value: @""]];
				[phoneNumbers replaceObjectAtIndex:[index unsignedIntegerValue] withObject:value];
				contact.phoneNumbers = phoneNumbers;
			}
			[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];
			
			break;
		}
		case PersonProfileItemIdEmail:
		{
			CpcContact* contact = item.referenceObject[@"contact"];
			NSNumber* index = item.referenceObject[@"index"];
			
			BOOL valueChanged = !([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil));
			BOOL labelChanged = (!([item.label isEqualToString:item.initialLabel] || ([item.label length] == 0 && item.initialLabel == nil))
													 || !([item.label isEqualToString:item.previousLabel] || ([item.label length] == 0 && item.previousLabel == nil)));
			
			if(!valueChanged && !labelChanged)
			{
				isChanged = NO;
			}
			else
			{
				if(labelChanged && !valueChanged && [item.value isEqualToString:@""])
				{
					isChanged = NO;
				}
			}
			
			if(valueChanged || labelChanged)
			{
				NSMutableArray<CNLabeledValue<NSString*>*> *emails = [contact.emails mutableCopy];
				CNLabeledValue<NSString*> *oldValue = emails[[index unsignedIntegerValue]];
				CNLabeledValue<NSString*> *value = [oldValue labeledValueBySettingLabel:item.label value:item.value != nil ? item.value: @""];
				[emails replaceObjectAtIndex:index.unsignedIntegerValue withObject:value];
				contact.emails = emails;
			}
			[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];
			
			break;
		}
		case PersonProfileItemIdURL:
		{
			CpcContact* contact = item.referenceObject[@"contact"];
			NSNumber* index = item.referenceObject[@"index"];
			
			BOOL valueChanged = !([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil));
			BOOL labelChanged = (!([item.label isEqualToString:item.initialLabel] || ([item.label length] == 0 && item.initialLabel == nil))
													 || !([item.label isEqualToString:item.previousLabel] || ([item.label length] == 0 && item.previousLabel == nil)));
			
			if(!valueChanged && !labelChanged)
			{
				isChanged = NO;
			}
			else
			{
				if(labelChanged && !valueChanged && [item.value isEqualToString:@""])
				{
					isChanged = NO;
				}
			}
			
			if(valueChanged || labelChanged)
			{
				NSMutableArray<CNLabeledValue<NSString*>*> *urls = [contact.URLs mutableCopy];
				CNLabeledValue<NSString*> *oldValue = urls[[index unsignedIntegerValue]];
				CNLabeledValue<NSString*> *value = [oldValue labeledValueBySettingLabel:item.label value:item.value != nil ? item.value: @""];
				[urls replaceObjectAtIndex:index.unsignedIntegerValue withObject:value];
				contact.URLs = urls;
			}
			
			[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];
			
			break;
		}
		case PersonProfileItemIdIM:
		{
			CpcContact* contact = item.referenceObject[@"contact"];
			NSNumber* index = item.referenceObject[@"index"];
			
			NSMutableArray<CNLabeledValue<CNInstantMessageAddress*>*> *ims = [contact.IMs mutableCopy];
			CNLabeledValue<CNInstantMessageAddress*> *oldValue = ims[[index unsignedIntegerValue]];
			CNLabeledValue<CNInstantMessageAddress*> *value = [oldValue labeledValueBySettingValue:[[CNInstantMessageAddress alloc] initWithUsername:item.value service:item.label]];
			[ims replaceObjectAtIndex:index.unsignedIntegerValue withObject:value];
			contact.IMs = ims;
			break;
		}
		case PersonProfileItemIdRingtone:
		{
			isChanged = !([_contact.ringtone isEqualToString:item.value] ||
										(_contact.ringtone == nil && [item.value length] == 0) ||
										([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)));
			_contact.ringtone = item.value;
			if(isChanged)
			{
				[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];
			}
		}
			break;
		case PersonProfileItemIdTexttone:
		{
			isChanged = !([_contact.texttone isEqualToString:item.value] ||
										(_contact.texttone == nil && [item.value length] == 0) ||
										([item.value isEqualToString:item.initialValue] || ([item.value length] == 0 && item.initialValue == nil)));
			
			_contact.texttone = item.value;
			if(isChanged)
			{
				[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];
			}
		}
			break;
		case PersonProfileItemIdSoftphone:
		{
			CpcContact* contact = item.referenceObject[@"contact"];
			NSNumber* index = item.referenceObject[@"index"];
			
			NSString *uriString = item.value;
			uriString = [CpcUtilsAddresses fixSipAddress:uriString];
			
			CpcUri *uri = [CpcUri uriWithNumber:uriString];
			
			if(uri != nil && (![uri isKindOfClass:[CpcSipUri class]] || uri.host.length == 0 || uri.user.length == 0))
			{
				NSString *user = uri.user.length > 0 ? uri.user: uri.host;
				uri = [[CpcSipUri alloc] initWithUser:user host:[CpcUtilsAddresses domainWithoutPort:self.account.domain]];
			}
			
			if(uri == nil)
				uri = [[CpcSipUri alloc] initWithString:@"sip:empty;newContact=1"];
			
			if(index.integerValue>=0 && index.integerValue<contact.softphones.count)
			{
				CpcSipUri *currentUri = contact.softphones[index.integerValue].value;
				isChanged = (currentUri != uri) && ![currentUri isAorEqual:uri];
			}
			else
			{
				isChanged = NO;
			}
			
			if (isChanged)
			{
				NSMutableArray<CNLabeledValue<CpcSipUri*> *> *softphones = [contact.softphones mutableCopy];
				CNLabeledValue<CpcSipUri*> *oldValue = softphones[[index unsignedIntegerValue]];
				CNLabeledValue<CpcSipUri*> *value = [oldValue labeledValueBySettingValue:(CpcSipUri*)uri];
				[softphones replaceObjectAtIndex:[index unsignedIntegerValue] withObject:value];
				contact.softphones = softphones;
				
				NSString *aor = [uri parameter:@"newContact"] != nil ? @"": uri.aor;
				item.value = aor;
				
				[self updatePresenceNumberWithNumber:aor];

				GroupedSection *section = [GroupedSection getGroupedSectionById:PersonProfileSectionIdSoftphones sections:self.bodyFields];

				[self.tableView reloadRowsAtIndexPaths:[NSArray arrayWithObject:[NSIndexPath indexPathForRow:index.unsignedIntegerValue inSection:[self.bodyFields indexOfObject:section]]] withRowAnimation:UITableViewRowAnimationNone];
			}
			[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];
			break;
		}
			
		case PersonProfileItemIdIMPS:
		{
			CpcSipUri *imAddress = nil;
			
			if(item.value != nil)
			{
				@try
				{
					imAddress = [[CpcSipUri alloc] initWithString:item.value];
				}
				@catch (NSException *exception)
				{
					imAddress = nil;
				}
			}
			
			CpcSipUri *currentIm = _contact.imAddress;
			
			isChanged = (imAddress != currentIm) && ![imAddress isAorEqual:currentIm];

			if(isChanged)
			{
				_contact.imAddress = imAddress;

				GroupedSection *section = [GroupedSection getGroupedSectionById:PersonProfileSectionIdImPresence sections:self.bodyFields];
				GroupedItem *item = section.items[0];
				item.value = [imAddress aor];
				
				dispatch_async(dispatch_get_main_queue(), ^
				{
					GroupedSection *section = [GroupedSection getGroupedSectionById:PersonProfileSectionIdImPresence sections:self.bodyFields];
					if(section)
					{
						[self.tableView reloadSections:[NSIndexSet indexSetWithIndex:[self.bodyFields indexOfObject:section]] withRowAnimation:UITableViewRowAnimationNone];
					}
				});
			}

			[self setTextFieldItem:item hasChange:[NSNumber numberWithBool:isChanged]];

			[self presenceDone:nil];
			break;
		}
		default:
			isChanged = NO;
			break;
	}
	
	
	if (isChanged && self.editing)
	{
		self.navigationItem.rightBarButtonItem.enabled = [self hasChangesToSave];
	}
}

- (void)itemClicked:(GroupedItem *)item inSection:(GroupedSection*)section
{
	[item.delegate itemChanged:item];
}

- (void)buttonClicked:(GroupedItem *)item buttonNumber:(NSInteger)number
{
	if (item.itemId == PersonProfileItemIdDeletePersonAction)
	{
		[self.view endEditing:YES];
		
		NSString *fullName = self.contact.fullName;
		NSString *alertMessage = [NSString stringWithFormat:NSLocalizedString(@"Are you sure you want to delete %@? %@ will also be deleted from your device’s contact list.", @"Confirmation message for deleting a contact"), fullName, fullName];
		
		__weak PersonProfileViewController *weakself = self;
		
		dispatch_async(dispatch_get_main_queue(), ^
									 {
			UIAlertController *alertController = [GlobalAlertController alertControllerWithTitle:NSLocalizedString(@"Delete Contact", @"Delete contact alert title")
																																							 message:alertMessage
																																				preferredStyle:UIAlertControllerStyleAlert];
			
			UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", @"Cancel button title") style:UIAlertActionStyleCancel handler:nil];
			UIAlertAction *deleteAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Delete", @"Delete button title") style:UIAlertActionStyleDestructive handler:^(UIAlertAction *action)
																		 {
				[[CpcServices instance].cpcContacts deleteContact:self.contact.identifier];
				weakself.editing = NO;
			}];
			
			[alertController addAction:cancelAction];
			[alertController addAction:deleteAction];
			
			cancelAction.accessibilityIdentifier = @"bCancel";
			deleteAction.accessibilityIdentifier = @"bDelete";
			
			[weakself presentViewController:alertController animated:YES completion:nil];
		});
	}
	else if(item.itemId == PersonProfileItemIdBlockCallerButton)
	{
		CpcUri* uri = item.referenceObject;
		__weak PersonProfileViewController *weakself = self;
		
		if(uri != nil)
		{
			if(![_callBlockingService isAddedToBlockList:uri])
			{
				UIAlertController* alert = [GlobalAlertController alertControllerWithTitle:nil
																																			 message:NSLocalizedString(@"You will not receive phone calls from numbers in block list.", @"")
																																preferredStyle:UIAlertControllerStyleActionSheet];
				alert.view.accessibilityIdentifier = @"vBlockAlert";
				
				[alert addAction:[UIAlertAction actionWithTitle:[NSString stringWithFormat:NSLocalizedString(@"Block %@", @"button title"), uri.user]
																									style:UIAlertActionStyleDestructive
																								handler:^(UIAlertAction * _Nonnull action) {
					[_callBlockingService addBlock:uri];
					[weakself setEditing:weakself.editing];
					
				}]];
				
				[alert addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", @"")
																									style:UIAlertActionStyleCancel
																								handler:nil]];
				
				[[AppController getRootViewController] presentViewController:alert animated:YES completion:nil];
				alert.popoverPresentationController.sourceView = [AppController getRootControllerView];
				alert.popoverPresentationController.sourceRect = [AppController getRootControllerView].bounds;
				alert.popoverPresentationController.permittedArrowDirections = 0;
			}
			else
			{
				[_callBlockingService removeBlock:uri];
				[self setEditing:self.editing];
			}
		}
	}
}

#pragma mark - UITableViewDataSource
- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath
{
	if (self.tableView == tableView)
	{
		if (_contact == nil) {
			LOG(EError, @"Attempting to commit editing style with a nil contact. Aborting.");
			return;
		}

		if (indexPath == nil) {
			LOG(EError, @"Attempting to commit editing style with a nil indexPath. Aborting.");
			return;
		}

		if (indexPath.section >= [self.bodyFields count]) {
			LOG1(EError, @"Attempting to commit editing style with invalid section index %ld. Aborting.", (long)indexPath.section);
			return;
		}

		GroupedSection* section = self.bodyFields[indexPath.section];

		if (section == nil) {
			LOG(EError, @"Attempting to commit editing style with a nil section. Aborting.");
			return;
		}
		if (editingStyle == UITableViewCellEditingStyleInsert)
		{
			if (indexPath.row > [section.items count]) {
				LOG1(EError, @"Attempting to insert item with invalid row index %ld. Aborting.", (long)indexPath.row);
				return;
			}

			GroupedItem* item = [GroupedItem createWithLabel:nil value:nil itemId:0 type:EGroupedItemContactInputText delegate:self];
			item.sectionId = section.sectionId;
			[section.items insertObject:item atIndex:indexPath.row];
			
			if (section.sectionId == PersonProfileSectionIdPhones)
			{
				CNLabeledValue<CNPhoneNumber*> *value = [[CNLabeledValue alloc] initWithLabel:CNLabelHome value:[[CNPhoneNumber alloc] initWithStringValue:@""]];
				NSMutableArray<CNLabeledValue<CNPhoneNumber*> *> *phoneNumbers = [_contact.phoneNumbers mutableCopy];
				[phoneNumbers addObject:value];
				_contact.phoneNumbers = phoneNumbers;
				item.label = CNLabelHome;
				item.itemId = PersonProfileItemIdPhone;
				item.referenceObject = @{@"contact": _contact, @"index":@([_contact.phoneNumbers count]-1)};
				item.accessibilityIdentifiers = @[@"lPhone", @"tPhone"];
			}
			else if (section.sectionId == PersonProfileSectionIdEmails)
			{
				CNLabeledValue<NSString*> *value = [[CNLabeledValue alloc] initWithLabel:CNLabelHome value:@""];
				NSMutableArray<CNLabeledValue<NSString*> *> *emails = [_contact.emails mutableCopy];
				[emails addObject:value];
				_contact.emails = emails;
				item.label = CNLabelHome;
				item.itemId = PersonProfileItemIdEmail;
				item.referenceObject = @{@"contact": _contact, @"index":@([_contact.emails count]-1)};
				item.accessibilityIdentifiers = @[@"lEmail", @"tEmail"];
			}
			else if (section.sectionId == PersonProfileSectionIdURLs)
			{
				CNLabeledValue<NSString*> *value = [[CNLabeledValue alloc] initWithLabel:CNLabelURLAddressHomePage value:@""];
				NSMutableArray<CNLabeledValue<NSString*> *> *urls = [_contact.URLs mutableCopy];
				[urls addObject:value];
				_contact.URLs = urls;
				item.label = CNLabelURLAddressHomePage;
				item.itemId = PersonProfileItemIdURL;
				item.referenceObject = @{@"contact": _contact, @"index":@([_contact.URLs count]-1)};
				item.accessibilityIdentifiers = @[@"lURL", @"tURL"];
			}
			else if (section.sectionId == PersonProfileSectionIdIMs)
			{
				CNLabeledValue<CNInstantMessageAddress*> *value = [[CNLabeledValue alloc] initWithLabel:nil value:[[CNInstantMessageAddress alloc] initWithUsername:@"" service:CNInstantMessageServiceJabber]];
				NSMutableArray<CNLabeledValue<CNInstantMessageAddress*> *> *ims = [_contact.IMs mutableCopy];
				[ims addObject:value];
				_contact.IMs = ims;
				item.label = @"im";
				item.itemId = PersonProfileItemIdIM;
				item.referenceObject = @{@"contact": _contact, @"index":@([_contact.IMs count]-1)};
			}
			else if (section.sectionId == PersonProfileSectionIdSoftphones)
			{
				NSMutableArray<CNLabeledValue<CpcSipUri*>*> *softphones = [_contact.softphones mutableCopy];
				[softphones addObject:[[CNLabeledValue alloc] initWithLabel:nil value:[[CpcSipUri alloc] initWithString:@"sip:empty;newContact=1"]]];
				_contact.softphones = softphones;
				item.label = NSLocalizedString(@"softphone", @"sip-uri label");
				item.itemId = PersonProfileItemIdSoftphone;
				item.referenceObject = @{@"contact": _contact, @"index":@([_contact.softphones count]-1)};
				item.accessibilityIdentifiers = @[@"lSoftphone", @"tSoftphone"];
			}
			
			[tableView insertRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
			
			dispatch_async(dispatch_get_main_queue(), ^
			{
				UITableViewCell* cell = [tableView cellForRowAtIndexPath:indexPath];
				if([cell respondsToSelector:@selector(contactWithInputTextType)])
				{
					TableViewCellContactWithInputText* tvc = (TableViewCellContactWithInputText*)cell;
					[tvc.textField becomeFirstResponder];
				}
			});
		}
		else if (editingStyle == UITableViewCellEditingStyleDelete)
		{
			[self.tableView findAndResignFirstResponder];

			if (indexPath.row >= [section.items count]) {
				LOG1(EError, @"Attempting to delete item with invalid row index %ld. Aborting.", (long)indexPath.row);
				return;
			}

			GroupedItem* item = section.items[indexPath.row];

			if (item == nil) {
				LOG(EError, @"Attempting to delete a nil item. Aborting.");
				return;
			}

			CpcContact* contact = item.referenceObject[@"contact"];
			NSNumber* index = item.referenceObject[@"index"];
			
			// re-arrange indexes of the multi-value list associate with this contact
			for (NSUInteger i = [section.items indexOfObject:item]+1; i < [section.items count]; ++i)
			{
				GroupedItem* nextItem = section.items[i];
				CpcContact* nextContact = nextItem.referenceObject[@"contact"];
				NSNumber* nextIndex = nextItem.referenceObject[@"index"];
				if (contact != nextContact)
					break;
				
				nextItem.referenceObject = @{@"contact": nextContact, @"index":@([nextIndex integerValue]-1)};
			}
			[section.items removeObject:item];
			
			if (section.sectionId == PersonProfileSectionIdPhones)
			{
				NSMutableArray<CNLabeledValue<CNPhoneNumber *> *> *phoneNumbers = [contact.phoneNumbers mutableCopy];
				[phoneNumbers removeObjectAtIndex:index.unsignedIntegerValue];
				contact.phoneNumbers = phoneNumbers;
			}
			else if (section.sectionId == PersonProfileSectionIdEmails)
			{
				NSMutableArray<CNLabeledValue<NSString *> *> *emails = [contact.emails mutableCopy];
				[emails removeObjectAtIndex:index.unsignedIntegerValue];
				contact.emails = emails;
			}
			else if (section.sectionId == PersonProfileSectionIdURLs)
			{
				NSMutableArray<CNLabeledValue<NSString *> *> *urls = [contact.URLs mutableCopy];
				[urls removeObjectAtIndex:index.unsignedIntegerValue];
				contact.URLs = urls;
			}
			else if (section.sectionId == PersonProfileSectionIdIMs)
			{
				NSMutableArray<CNLabeledValue<CNInstantMessageAddress *> *> *ims = [contact.IMs mutableCopy];
				[ims removeObjectAtIndex:index.unsignedIntegerValue];
				contact.IMs = ims;
			}
			else if (section.sectionId == PersonProfileSectionIdSoftphones)
			{
				NSMutableArray<CNLabeledValue<CpcSipUri*>*> *softphones = [contact.softphones mutableCopy];
				[softphones removeObjectAtIndex:index.unsignedIntegerValue];
				contact.softphones = softphones;
				[self updatePresenceNumberWithNumber:nil];
			}
			
			[self itemWillBeDeleted:item];
			[tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationLeft];
		}
	}
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
	[super tableView:tableView didSelectRowAtIndexPath:indexPath];
	
	if (tableView != self.tableView)
		return;
	
	GroupedSection* section = self.bodyFields[indexPath.section];
	switch(section.sectionId)
	{
		case PersonProfileSectionIdPhones:
		case PersonProfileSectionIdEmails:
		case PersonProfileSectionIdIMs:
		case PersonProfileSectionIdURLs:
		{
			if (tableView.editing == NO)
				return;
			
			GroupedItem* item = section.items [indexPath.row];
			if (item.itemId == PersonProfileItemIdAddItemAction)
			{
				[self tableView:tableView commitEditingStyle:UITableViewCellEditingStyleInsert forRowAtIndexPath:indexPath];
			}
			else
			{
				[self showLabelSelectionsForIndexPath:indexPath];
			}
			break;
		}
			
		case PersonProfileSectionIdUserIds:
		case PersonProfileSectionIdSoftphones:
		{
			if (tableView.editing == NO)
				return;

			GroupedItem* item = section.items [indexPath.row];
			if (item.itemId == PersonProfileItemIdAddItemAction)
			{
				[self tableView:tableView commitEditingStyle:UITableViewCellEditingStyleInsert forRowAtIndexPath:indexPath];
			}
			else
			{
				TableViewCellContactWithInputText *cell = (TableViewCellContactWithInputText*)[tableView cellForRowAtIndexPath:indexPath];
				
				if([cell isKindOfClass:[TableViewCellContactWithInputText class]] && [cell.textField isFirstResponder] == NO)
				{
					[cell.textField becomeFirstResponder];
				}
			}
			
			break;
		}
			
		case PersonProfileSectionIdTones:
			[self showToneSelectionForIndexPath:indexPath];
			break;
		case PersonProfileSectionIdImPresence:
		{
			if(self.tableView.editing)
			{
				dispatch_async(dispatch_get_main_queue(), ^
				{
					[self showPresenceView:YES];
				});
			}
			break;
		}
		default:
			return;
	}
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath
{
	[super tableView:tableView willDisplayCell:cell forRowAtIndexPath:indexPath];
	
	if (tableView != self.tableView)
		return;
	
	GroupedSection* section = self.bodyFields[indexPath.section];
	GroupedItem* item = section.items[indexPath.row];
	CpcContact* contact = item.referenceObject[@"contact"];
	NSNumber* index = item.referenceObject[@"index"];
	switch (item.itemId)
	{
		case PersonProfileItemIdPhone:
		{
			NSString* localizedString = [CNLabeledValue localizedStringForLabel:contact.phoneNumbers[index.unsignedIntegerValue].label];
			cell.textLabel.text = [localizedString length] ? localizedString : NSLocalizedString(@"Phone", @"");
			break;
		}
		case PersonProfileItemIdEmail:
		{
			NSString* localizedString = [CNLabeledValue localizedStringForLabel:contact.emails[index.unsignedIntegerValue].label];
			cell.textLabel.text = [localizedString length] ? localizedString : NSLocalizedString(@"Email", @"");
			break;
		}
		case PersonProfileItemIdURL:
		{
			NSString* localizedString = [CNLabeledValue localizedStringForLabel:contact.URLs[index.unsignedIntegerValue].label];
			if(localizedString != nil)
			{
				cell.textLabel.text = localizedString;
			}
			else
			{
				NSURL *url = [NSURL URLWithString:contact.URLs[index.unsignedIntegerValue].value];
				if([url.scheme.lowercaseString isEqualToString:@"ptt"])
				{
					NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
					
					if([components.queryItems indexOfObjectPassingTest:^BOOL(NSURLQueryItem *obj, NSUInteger idx, BOOL *stop) {
						return [obj.name isEqualToString:@"type"] && [obj.value isEqualToString:@"channel"];
					}] != NSNotFound)
					{
						cell.textLabel.text = NSLocalizedString(@"PTT Channel", @"");
					}
					else
					{
						cell.textLabel.text = NSLocalizedString(@"PTT", @"");
						cell.detailTextLabel.text = NSLocalizedString(@"Call via Push To Talk", @"");
					}
				}
				else
				{
					cell.textLabel.text = NSLocalizedString(@"URL", @"");
				}
			}
			break;
		}
		case PersonProfileItemIdIM:
		{
			NSString* value = [CNLabeledValue localizedStringForLabel:contact.IMs[index.unsignedIntegerValue].label];
			cell.textLabel.text = value.length ? value : NSLocalizedString(@"IM", @"");
			break;
		}
		case PersonProfileItemIdRingtone:
		{
			NSString *name = [AppUtils getToneName:item.value];
			cell.detailTextLabel.text = name != nil ? name: [AppUtils getRingtoneName:item.value];
		}
			break;
		case PersonProfileItemIdTexttone:
		{
			NSString *name = [AppUtils getToneName:item.value];
			cell.detailTextLabel.text = name != nil ? name: [AppUtils getImtoneName:item.value];
		}
			break;
		default:
			break;
	}
	
	BOOL readonly = contact.readonly;
	TableViewCell* tvc = (TableViewCell*)cell;
	tvc.userInteractionEnabled = !readonly || !tableView.editing;
	[tvc setEditable:!readonly andSelectable:tvc.userInteractionEnabled];
}

- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath;
{
	UITableViewCellEditingStyle style = [super tableView:tableView editingStyleForRowAtIndexPath:indexPath];
	
	if (tableView != self.tableView)
		return style;
	
	GroupedSection* section = self.bodyFields[indexPath.section];
	GroupedItem* item = section.items[indexPath.row];
	CpcContact* contact = item.referenceObject[@"contact"];
	BOOL readonly = contact.readonly;
	
	return readonly ? UITableViewCellEditingStyleNone : style;
}

@end
